/** @format */

'use client';

import React from 'react';
import { HeroSection } from './hero';
import { CategoryExplorer, AIShowcase } from './features';
import LandingFooter from './footer/LandingFooter';

interface FloatingHeroIslandsProps {
	onGetStarted: () => void;
}

const FloatingHeroIslands: React.FC<FloatingHeroIslandsProps> = ({
	onGetStarted,
}) => {
	return (
		<div className='relative'>
			{/* Hero Section */}
			<HeroSection onGetStarted={onGetStarted} />

			{/* Feature Sections */}
			<div className='relative'>
				{/* Category Explorer Section */}
				<CategoryExplorer />

				{/* AI Showcase Section */}
				<AIShowcase />
			</div>

			{/* Footer */}
			<LandingFooter />
		</div>
	);
};

export default FloatingHeroIslands;
