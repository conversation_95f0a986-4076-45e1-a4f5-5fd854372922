/** @format */

// Main landing page component
export { default as LandingPage } from './LandingPage';

// Main container component (includes all modular sections)
export { default as FloatingHeroIslands } from './FloatingHeroIslands';

// Modular components (can be used individually)
export * from './features';
export * from './footer';
export * from './hero';

// Legacy components (kept for backward compatibility)
export { default as AIPlaygroundDemo } from './AIPlaygroundDemo';
export { default as DynamicCategoryMosaic } from './DynamicCategoryMosaic';
export { default as ParallaxFeatureSection } from './ParallaxFeatureSection';
