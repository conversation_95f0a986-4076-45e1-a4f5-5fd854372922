/** @format */

// Main landing page component
export { default as LandingPage } from './LandingPage';

// Hero section modules
export { default as HeroBranding } from './hero/HeroBranding';
export { default as HeroPreview } from './hero/HeroPreview';
export { default as HeroSection } from './hero/HeroSection';

// Features section modules
export { default as AIShowcase } from './features/AIShowcase';
export { default as CategoryExplorer } from './features/CategoryExplorer';

// Footer section modules
export { default as LandingFooter } from './footer/LandingFooter';

// Legacy components (for backward compatibility)
export { default as AIPlaygroundDemo } from './AIPlaygroundDemo';
export { default as DynamicCategoryMosaic } from './DynamicCategoryMosaic';
export { default as Footer } from './Footer';
export { default as ParallaxFeatureSection } from './ParallaxFeatureSection';
