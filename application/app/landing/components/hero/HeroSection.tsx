/** @format */

'use client';

import { colors } from '@/app/colors';
import React from 'react';
import HeroBranding from './HeroBranding';
import HeroPreview from './HeroPreview';

interface HeroSectionProps {
	onGetStarted: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
	return (
		<div className='relative min-h-screen overflow-hidden bg-transparent'>
			{/* Background Features */}
			{React.createElement(require('../ParallaxFeatureSection').default)}

			{/* Particle Background */}
			<div className='absolute inset-0'>
				{/* Animated background particles */}
				{Array.from({ length: 20 }).map((_, i) => (
					<div
						key={i}
						className='absolute w-2 h-2 rounded-full opacity-30 animate-pulse'
						style={{
							left: `${Math.random() * 100}%`,
							top: `${Math.random() * 100}%`,
							background:
								i % 3 === 0
									? colors.brand.blue
									: i % 3 === 1
									? colors.brand.green
									: colors.supporting.lightBlue,
							animationDelay: `${Math.random() * 3}s`,
							animationDuration: `${2 + Math.random() * 2}s`,
						}}></div>
				))}
			</div>

			{/* Main Content Container */}
			<div className='relative min-h-screen py-12 lg:py-20'>
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
					{/* Mobile/Small Screen Layout - Centered Single Column */}
					<div className='flex flex-col space-y-12 min-h-[80vh] justify-center lg:hidden'>
						{/* Company Branding - Centered */}
						<div className='flex justify-center'>
							<HeroBranding />
						</div>

						{/* Hero Preview - Stacked */}
						<div className='flex justify-center'>
							<HeroPreview onGetStarted={onGetStarted} />
						</div>
					</div>

					{/* Large Screen Layout - Two Column */}
					<div className='hidden lg:grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]'>
						{/* Left Side - Company Branding */}
						<div className='flex items-center justify-start'>
							<HeroBranding />
						</div>

						{/* Right Side - Hero Preview */}
						<div className='flex items-center justify-center'>
							<HeroPreview onGetStarted={onGetStarted} />
						</div>
					</div>
				</div>
			</div>

			{/* Scroll Indicator */}
			<div className='absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce'>
				<div
					className='w-6 h-10 border-2 rounded-full flex justify-center'
					style={{ borderColor: colors.brand.blue }}>
					<div
						className='w-1 h-3 rounded-full mt-2 animate-pulse'
						style={{ background: colors.brand.blue }}></div>
				</div>
			</div>
		</div>
	);
};

export default HeroSection;
