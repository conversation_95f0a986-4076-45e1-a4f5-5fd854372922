/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	LANDING_PAGE_DATA,
	getPOICategories,
	getPOISubcategories,
} from '@/app/shared/poi/constants';
import React, { useState } from 'react';
import { FiGlobe, FiMapPin, FiMessageCircle } from 'react-icons/fi';

const HeroBranding: React.FC = () => {
	// Get real data counts
	const categoriesCount = getPOICategories().length;
	const subcategoriesCount = getPOISubcategories().length;
	const [isHovered, setIsHovered] = useState(false);

	return (
		<div className='relative w-full max-w-2xl mx-auto px-6'>
			{/* Revolutionary Vertical Card Design */}
			<div
				className='relative overflow-hidden rounded-3xl backdrop-blur-xl border border-white/20 transition-all duration-700 hover:scale-[1.02]'
				style={{
					background: `
						linear-gradient(135deg,
							rgba(255, 255, 255, 0.1) 0%,
							rgba(255, 255, 255, 0.05) 50%,
							rgba(51, 194, 255, 0.1) 100%
						)
					`,
					boxShadow: `
						0 25px 50px rgba(51, 194, 255, 0.15),
						inset 0 1px 0 rgba(255, 255, 255, 0.2)
					`,
				}}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}>
				{/* Animated Background Pattern */}
				<div className='absolute inset-0 opacity-30'>
					<div
						className='absolute top-0 left-0 w-full h-full'
						style={{
							background: `
								radial-gradient(circle at 20% 20%, ${colors.brand.blue}20 0%, transparent 50%),
								radial-gradient(circle at 80% 80%, ${colors.brand.green}20 0%, transparent 50%),
								radial-gradient(circle at 40% 60%, ${colors.brand.navy}15 0%, transparent 50%)
							`,
							transform: isHovered ? 'scale(1.1) rotate(5deg)' : 'scale(1)',
							transition: 'transform 0.7s ease-out',
						}}
					/>
				</div>

				{/* Content Container */}
				<div className='relative z-10 p-8 space-y-8'>
					{/* Header Section with Logo Integration */}
					<div className='text-center space-y-4'>
						{/* Logo with Floating Effect */}
						<div className='flex justify-center mb-6'>
							<div
								className='relative p-4 rounded-2xl transition-all duration-500'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.blue}20 0%, ${colors.brand.green}20 100%)`,
									transform: isHovered
										? 'translateY(-5px) rotate(5deg)'
										: 'translateY(0) rotate(0deg)',
								}}>
								<img
									src='/logo/512x512.png'
									alt='Wizlop Logo'
									className='w-16 h-16 object-contain transition-all duration-500'
									style={{
										filter: `brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(2555%) hue-rotate(202deg) brightness(101%) contrast(101%)`,
										transform: isHovered ? 'scale(1.1)' : 'scale(1)',
									}}
								/>
								{/* Floating particles around logo */}
								<div
									className='absolute -top-1 -right-1 w-3 h-3 rounded-full animate-bounce'
									style={{
										background: colors.brand.blue,
										animationDelay: '0s',
									}}
								/>
								<div
									className='absolute -bottom-1 -left-1 w-2 h-2 rounded-full animate-bounce'
									style={{
										background: colors.brand.green,
										animationDelay: '1s',
									}}
								/>
							</div>
						</div>

						{/* Company Name with Dynamic Typography */}
						<h1 className='text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight leading-none'>
							<span
								className='bg-clip-text text-transparent block'
								style={{
									backgroundImage: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 50%, ${colors.brand.green} 100%)`,
									WebkitBackgroundClip: 'text',
									WebkitTextFillColor: 'transparent',
									transform: isHovered ? 'scale(1.05)' : 'scale(1)',
									transition: 'transform 0.5s ease-out',
								}}>
								{LANDING_PAGE_DATA.branding.companyName}
							</span>
						</h1>

						{/* Animated Tagline */}
						<div className='relative'>
							<p
								className='text-xl sm:text-2xl font-bold tracking-wide transition-all duration-500'
								style={{
									color: colors.brand.blue,
									transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
								}}>
								{LANDING_PAGE_DATA.branding.tagline}
							</p>
							{/* Dynamic underline */}
							<div
								className='absolute -bottom-1 left-1/2 transform -translate-x-1/2 h-1 rounded-full transition-all duration-500'
								style={{
									background: `linear-gradient(90deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
									width: isHovered ? '80%' : '40%',
								}}
							/>
						</div>
					</div>

					{/* Hero Content with Glassmorphism */}
					<div
						className='relative p-6 rounded-2xl backdrop-blur-sm border border-white/10 transition-all duration-500'
						style={{
							background: `linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)`,
							transform: isHovered ? 'translateY(-3px)' : 'translateY(0)',
						}}>
						{/* Hero Title */}
						<h2 className='text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight mb-4 text-center'>
							<span style={{ color: colors.brand.navy }}>
								{LANDING_PAGE_DATA.branding.heroTitle}
							</span>
						</h2>

						{/* Description */}
						<p
							className='text-base sm:text-lg leading-relaxed opacity-90 text-center max-w-lg mx-auto mb-6'
							style={{ color: colors.neutral.slateGray }}>
							{LANDING_PAGE_DATA.branding.heroDescription}
						</p>

						{/* Interactive Feature Badge */}
						<div className='flex justify-center'>
							{LANDING_PAGE_DATA.getFeatureBadges()
								.slice(0, 1)
								.map(({ icon, text }, index) => {
									const iconMap: { [key: string]: React.ComponentType<any> } = {
										FiGlobe,
										FiMessageCircle,
										FiMapPin,
									};
									const Icon = iconMap[icon] || FiMapPin;
									return (
										<div
											key={index}
											className='group relative inline-flex items-center space-x-3 px-6 py-3 rounded-full backdrop-blur-md border transition-all duration-300 hover:scale-105 cursor-pointer'
											style={{
												background: `linear-gradient(135deg, ${colors.ui.blue50}F0 0%, ${colors.ui.green50}F0 100%)`,
												borderColor: colors.ui.gray200,
												boxShadow: '0 8px 25px rgba(51, 194, 255, 0.15)',
											}}>
											{/* Icon with rotation effect */}
											<Icon
												className='w-5 h-5 transition-transform duration-300 group-hover:rotate-12'
												style={{ color: colors.brand.blue }}
											/>
											<span
												className='font-semibold transition-all duration-300'
												style={{ color: colors.neutral.textBlack }}>
												{text}
											</span>
											{/* Hover glow effect */}
											<div
												className='absolute inset-0 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300'
												style={{
													background: `radial-gradient(circle, ${colors.brand.blue} 0%, transparent 70%)`,
												}}
											/>
										</div>
									);
								})}
						</div>
					</div>

					{/* Revolutionary Stats Section */}
					<div className='relative'>
						{/* Background glow */}
						<div
							className='absolute inset-0 rounded-2xl opacity-50 transition-all duration-500'
							style={{
								background: `radial-gradient(ellipse at center, ${colors.brand.blue}20 0%, transparent 70%)`,
								transform: isHovered ? 'scale(1.1)' : 'scale(1)',
							}}
						/>

						{/* Stats container */}
						<div
							className='relative flex items-center justify-center space-x-8 p-6 rounded-2xl backdrop-blur-md border border-white/20 transition-all duration-500'
							style={{
								background: `linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)`,
								transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
							}}>
							{/* Categories stat */}
							<div className='text-center group'>
								<div
									className='text-3xl font-black transition-all duration-300 group-hover:scale-110'
									style={{ color: colors.brand.blue }}>
									{categoriesCount}
								</div>
								<div
									className='text-sm font-medium transition-colors duration-300'
									style={{ color: colors.neutral.slateGray }}>
									Categories
								</div>
								{/* Animated dot */}
								<div
									className='w-2 h-2 rounded-full mx-auto mt-1 animate-pulse'
									style={{ background: colors.brand.blue }}
								/>
							</div>

							{/* Divider with gradient */}
							<div
								className='w-px h-12 transition-all duration-300'
								style={{
									background: `linear-gradient(to bottom, transparent 0%, ${colors.ui.gray200} 50%, transparent 100%)`,
									transform: isHovered ? 'scaleY(1.2)' : 'scaleY(1)',
								}}
							/>

							{/* Subcategories stat */}
							<div className='text-center group'>
								<div
									className='text-3xl font-black transition-all duration-300 group-hover:scale-110'
									style={{ color: colors.brand.green }}>
									{subcategoriesCount}
								</div>
								<div
									className='text-sm font-medium transition-colors duration-300'
									style={{ color: colors.neutral.slateGray }}>
									Subcategories
								</div>
								{/* Animated dot */}
								<div
									className='w-2 h-2 rounded-full mx-auto mt-1 animate-pulse'
									style={{
										background: colors.brand.green,
										animationDelay: '0.5s',
									}}
								/>
							</div>
						</div>
					</div>

					{/* Floating Action Elements */}
					<div className='absolute top-4 right-4 space-y-2'>
						<div
							className='w-3 h-3 rounded-full animate-bounce'
							style={{ background: colors.brand.blue, animationDelay: '0s' }}
						/>
						<div
							className='w-2 h-2 rounded-full animate-bounce'
							style={{ background: colors.brand.green, animationDelay: '0.3s' }}
						/>
						<div
							className='w-2 h-2 rounded-full animate-bounce'
							style={{ background: colors.brand.navy, animationDelay: '0.6s' }}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroBranding;
