/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';
import { <PERSON>Layers, FiTarget, FiZap } from 'react-icons/fi';

const HeroBranding: React.FC = () => {
	const [currentWordIndex, setCurrentWordIndex] = useState(0);
	const [isAnimating, setIsAnimating] = useState(false);
	const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

	// Dynamic words for the tagline animation
	const dynamicWords = [
		'Perfect Spot',
		'Hidden Gem',
		'Next Adventure',
		'Dream Location',
		'Ideal Place',
	];

	// Auto-cycle through words
	useEffect(() => {
		const interval = setInterval(() => {
			setIsAnimating(true);
			setTimeout(() => {
				setCurrentWordIndex((prev) => (prev + 1) % dynamicWords.length);
				setIsAnimating(false);
			}, 300);
		}, 3000);

		return () => clearInterval(interval);
	}, []);

	// Mouse tracking for interactive effects
	useEffect(() => {
		const handleMouseMove = (e: MouseEvent) => {
			setMousePosition({ x: e.clientX, y: e.clientY });
		};

		window.addEventListener('mousemove', handleMouseMove);
		return () => window.removeEventListener('mousemove', handleMouseMove);
	}, []);

	return (
		<div className='relative w-full max-w-2xl mx-auto px-4'>
			{/* Revolutionary Floating Card Design */}
			<div className='relative'>
				{/* Background Glow Effect */}
				<div
					className='absolute inset-0 rounded-3xl opacity-20 blur-xl'
					style={{
						background: `radial-gradient(ellipse at center, ${colors.brand.blue} 0%, ${colors.brand.green} 50%, transparent 100%)`,
						transform: `translate(${mousePosition.x * 0.01}px, ${
							mousePosition.y * 0.01
						}px)`,
					}}></div>

				{/* Main Floating Card */}
				<div
					className='relative backdrop-blur-xl rounded-3xl border border-white/20 p-8 lg:p-12 shadow-2xl'
					style={{
						background: `linear-gradient(135deg,
							rgba(255, 255, 255, 0.1) 0%,
							rgba(255, 255, 255, 0.05) 50%,
							rgba(255, 255, 255, 0.1) 100%)`,
						boxShadow: `
							0 25px 50px rgba(51, 194, 255, 0.15),
							inset 0 1px 0 rgba(255, 255, 255, 0.2),
							0 0 0 1px rgba(255, 255, 255, 0.1)
						`,
					}}>
					{/* Floating Logo with Orbital Animation */}
					<div className='relative mb-8 flex justify-center'>
						<div className='relative'>
							{/* Orbital Rings */}
							<div
								className='absolute inset-0 w-24 h-24 rounded-full border border-blue-200/30 animate-spin'
								style={{ animationDuration: '20s' }}></div>
							<div
								className='absolute inset-2 w-20 h-20 rounded-full border border-green-200/30 animate-spin'
								style={{
									animationDuration: '15s',
									animationDirection: 'reverse',
								}}></div>

							{/* Logo Container */}
							<div
								className='relative w-16 h-16 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/30 hover:scale-110 transition-all duration-500'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.blue}20 0%, ${colors.brand.green}20 100%)`,
								}}>
								<img
									src='/logo/512x512.png'
									alt='Wizlop Logo'
									className='w-10 h-10 object-contain'
									style={{
										filter: `brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(2555%) hue-rotate(202deg) brightness(101%) contrast(101%)`,
									}}
								/>
							</div>
						</div>
					</div>

					{/* Company Name with Morphing Effect */}
					<div className='text-center mb-6'>
						<h1 className='text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight leading-none mb-4'>
							<span
								className='bg-clip-text text-transparent block'
								style={{
									backgroundImage: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 50%, ${colors.brand.green} 100%)`,
									WebkitBackgroundClip: 'text',
									WebkitTextFillColor: 'transparent',
								}}>
								{LANDING_PAGE_DATA.branding.companyName}
							</span>
						</h1>

						{/* Dynamic Tagline with Word Animation */}
						<div className='relative h-8 mb-6'>
							<p className='text-xl sm:text-2xl font-bold flex items-center justify-center gap-2'>
								<span style={{ color: colors.brand.blue }}>Find Your</span>
								<span
									className={`inline-block transition-all duration-300 ${
										isAnimating
											? 'opacity-0 transform scale-95'
											: 'opacity-100 transform scale-100'
									}`}
									style={{
										background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
										WebkitBackgroundClip: 'text',
										WebkitTextFillColor: 'transparent',
										backgroundClip: 'text',
									}}>
									{dynamicWords[currentWordIndex]}
								</span>
							</p>
						</div>
					</div>

					{/* Hero Content with Floating Elements */}
					<div className='text-center space-y-6 mb-8'>
						<h2 className='text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight'>
							<span style={{ color: colors.brand.navy }}>
								{LANDING_PAGE_DATA.branding.heroTitle}
							</span>
						</h2>

						<p
							className='text-lg sm:text-xl leading-relaxed opacity-90 max-w-xl mx-auto'
							style={{ color: colors.neutral.slateGray }}>
							{LANDING_PAGE_DATA.branding.heroDescription}
						</p>
					</div>

					{/* Interactive Feature Badges */}
					<div className='flex flex-wrap justify-center gap-4 mb-8'>
						{[
							{ icon: FiZap, text: 'AI-Powered', color: colors.brand.blue },
							{
								icon: FiTarget,
								text: 'Precise Results',
								color: colors.brand.green,
							},
							{
								icon: FiLayers,
								text: '200+ Categories',
								color: colors.brand.navy,
							},
						].map((badge, index) => {
							const Icon = badge.icon;
							return (
								<div
									key={index}
									className='group relative overflow-hidden rounded-2xl backdrop-blur-sm border border-white/20 px-4 py-3 hover:scale-105 transition-all duration-300 cursor-pointer'
									style={{
										background: `linear-gradient(135deg, ${badge.color}10 0%, ${badge.color}05 100%)`,
										boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
									}}>
									{/* Hover Effect Background */}
									<div
										className='absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300'
										style={{
											background: `linear-gradient(135deg, ${badge.color}20 0%, ${badge.color}10 100%)`,
										}}></div>

									<div className='relative flex items-center space-x-2'>
										<Icon
											className='w-4 h-4 transition-transform duration-300 group-hover:scale-110'
											style={{ color: badge.color }}
										/>
										<span
											className='text-sm font-semibold'
											style={{ color: colors.neutral.textBlack }}>
											{badge.text}
										</span>
									</div>
								</div>
							);
						})}
					</div>

					{/* Floating Stats with Pulse Animation */}
					<div className='flex justify-center'>
						<div className='flex items-center space-x-8'>
							{[
								{ value: '8', label: 'Categories', color: colors.brand.blue },
								{
									value: '96',
									label: 'Subcategories',
									color: colors.brand.green,
								},
							].map((stat, index) => (
								<div
									key={index}
									className='text-center group'>
									<div
										className='text-3xl font-black mb-1 transition-all duration-300 group-hover:scale-110'
										style={{ color: stat.color }}>
										{stat.value}
									</div>
									<div
										className='text-sm font-medium'
										style={{ color: colors.neutral.slateGray }}>
										{stat.label}
									</div>
									{/* Pulse Effect */}
									<div
										className='w-8 h-0.5 mx-auto mt-2 rounded-full opacity-50 group-hover:opacity-100 transition-opacity duration-300'
										style={{ background: stat.color }}></div>
								</div>
							))}
						</div>
					</div>
				</div>

				{/* Floating Particles */}
				{Array.from({ length: 6 }).map((_, i) => (
					<div
						key={i}
						className='absolute w-2 h-2 rounded-full opacity-40 animate-pulse'
						style={{
							background: i % 2 === 0 ? colors.brand.blue : colors.brand.green,
							left: `${20 + i * 15}%`,
							top: `${10 + i * 10}%`,
							animationDelay: `${i * 0.5}s`,
							animationDuration: `${2 + i * 0.3}s`,
						}}></div>
				))}
			</div>
		</div>
	);
};

export default HeroBranding;
