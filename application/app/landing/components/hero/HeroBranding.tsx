/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React from 'react';
import {
	FiGlobe,
	FiMapPin,
	FiMessageCircle,
} from 'react-icons/fi';

const HeroBranding: React.FC = () => (
	<div className='relative w-full max-w-lg mx-auto px-4'>
		{/* Professional Header Layout - Contained and Non-Invasive */}
		<div className='space-y-8'>
			{/* Top Row - Logo and Company Name in Horizontal Layout */}
			<div className='flex items-center justify-between'>
				{/* Company Name - Left Side */}
				<div className='flex-1 min-w-0'>
					<h1 className='text-3xl sm:text-4xl lg:text-5xl font-black tracking-tight leading-none'>
						<span
							className='bg-clip-text text-transparent'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
								WebkitBackgroundClip: 'text',
								WebkitTextFillColor: 'transparent',
							}}>
							{LANDING_PAGE_DATA.branding.companyName}
						</span>
					</h1>
					{/* Decorative accent line */}
					<div
						className='h-1 w-16 rounded-full mt-2'
						style={{
							background: `linear-gradient(90deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
						}}></div>
				</div>

				{/* Logo - Right Side (No Background, Blue Color) */}
				<div className='flex-shrink-0 ml-6'>
					<img
						src='/logo/512x512.png'
						alt='Wizlop Logo'
						className='w-12 h-12 sm:w-16 sm:h-16 object-contain transition-transform duration-300 hover:scale-110'
						style={{
							filter: `brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(2555%) hue-rotate(202deg) brightness(101%) contrast(101%)`,
						}}
					/>
				</div>
			</div>

			{/* Content Section - Centered and Compact */}
			<div className='text-center space-y-4'>
				{/* Tagline */}
				<p
					className='text-lg sm:text-xl font-semibold'
					style={{ color: colors.brand.blue }}>
					{LANDING_PAGE_DATA.branding.tagline}
				</p>

				{/* Hero Title */}
				<h2 className='text-xl sm:text-2xl lg:text-3xl font-bold leading-tight'>
					<span style={{ color: colors.brand.navy }}>
						{LANDING_PAGE_DATA.branding.heroTitle}
					</span>
				</h2>

				{/* Description */}
				<p
					className='text-sm sm:text-base leading-relaxed opacity-90 max-w-md mx-auto'
					style={{ color: colors.neutral.slateGray }}>
					{LANDING_PAGE_DATA.branding.heroDescription}
				</p>

				{/* Feature Badge */}
				<div className='flex justify-center pt-2'>
					{LANDING_PAGE_DATA.getFeatureBadges()
						.slice(0, 1)
						.map(({ icon, text }, index) => {
							const iconMap: { [key: string]: React.ComponentType<any> } = {
								FiGlobe,
								FiMessageCircle,
								FiMapPin,
							};
							const Icon = iconMap[icon] || FiMapPin;
							return (
								<div
									key={index}
									className='inline-flex items-center space-x-2 px-4 py-2 rounded-full backdrop-blur-sm border transform hover:scale-105 transition-all duration-300'
									style={{
										background: `linear-gradient(135deg, ${colors.ui.blue50}E0 0%, ${colors.ui.green50}E0 100%)`,
										borderColor: colors.ui.gray200,
										boxShadow: '0 4px 12px rgba(51, 194, 255, 0.1)',
									}}>
									<Icon
										className='w-4 h-4'
										style={{ color: colors.brand.blue }}
									/>
									<span
										className='text-sm font-semibold'
										style={{ color: colors.neutral.textBlack }}>
										{text}
									</span>
								</div>
							);
						})}
				</div>
			</div>
		</div>

		{/* Stats Section - Compact */}
		<div className='mt-12 flex justify-center'>
			<div className='inline-flex items-center space-x-6 px-6 py-3 rounded-xl backdrop-blur-sm border'>
				<div className='text-center'>
					<div
						className='text-2xl font-black'
						style={{ color: colors.brand.blue }}>
						8
					</div>
					<div
						className='text-xs font-medium'
						style={{ color: colors.neutral.slateGray }}>
						Categories
					</div>
				</div>
				<div
					className='w-px h-6'
					style={{ background: colors.ui.gray200 }}></div>
				<div className='text-center'>
					<div
						className='text-2xl font-black'
						style={{ color: colors.brand.green }}>
						96
					</div>
					<div
						className='text-xs font-medium'
						style={{ color: colors.neutral.slateGray }}>
						Subcategories
					</div>
				</div>
			</div>
		</div>
	</div>
);

export default HeroBranding;
