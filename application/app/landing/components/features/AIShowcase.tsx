/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React from 'react';
import { FiMessageCircle } from 'react-icons/fi';
import AIPlaygroundDemo from '../AIPlaygroundDemo';

interface AIShowcaseProps {
	onGetStarted: () => void;
}

const AIShowcase: React.FC<AIShowcaseProps> = ({ onGetStarted }) => {
	return (
		<div className='py-16 bg-transparent'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-12'>
					<div
						className='inline-flex items-center space-x-2 rounded-full px-6 py-3 mb-8 border'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<FiMessageCircle
							className='w-5 h-5'
							style={{ color: colors.brand.green }}
						/>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{LANDING_PAGE_DATA.sectionHeaders.aiDemo.badge}
						</span>
					</div>

					<h2 className='text-5xl md:text-6xl font-bold mb-6'>
						<span style={{ color: colors.brand.navy }}>
							{LANDING_PAGE_DATA.sectionHeaders.aiDemo.title}
						</span>
						<br />
						<span
							className='text-transparent bg-clip-text'
							style={{
								backgroundImage: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
							}}>
							{LANDING_PAGE_DATA.sectionHeaders.aiDemo.subtitle}
						</span>
					</h2>

					<p
						className='text-xl max-w-3xl mx-auto leading-relaxed mb-8'
						style={{ color: colors.neutral.slateGray }}>
						{LANDING_PAGE_DATA.sectionHeaders.aiDemo.description}
					</p>
				</div>

				{/* AI Playground Demo - Keeping existing design */}
				<AIPlaygroundDemo onGetStarted={onGetStarted} />
			</div>
		</div>
	);
};

export default AIShowcase;
