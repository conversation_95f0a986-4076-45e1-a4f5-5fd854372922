/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useState, useEffect } from 'react';
import {
	FiMessageCircle,
	FiZap,
	FiMapPin,
	FiStar,
	FiClock,
	FiTrendingUp,
} from 'react-icons/fi';

const AIShowcase: React.FC = () => {
	const [currentStep, setCurrentStep] = useState(0);
	const [isAnimating, setIsAnimating] = useState(false);

	// AI conversation steps for demonstration
	const conversationSteps = [
		{
			type: 'user',
			message: 'Find me a cozy coffee shop with good WiFi for working',
			timestamp: '2:34 PM',
		},
		{
			type: 'ai',
			message: 'I found 3 perfect spots for you! Here are coffee shops with excellent WiFi and work-friendly atmosphere...',
			timestamp: '2:34 PM',
			results: [
				{ name: 'Brew & Bytes Café', rating: 4.8, distance: '0.3 km' },
				{ name: 'The Digital Grind', rating: 4.7, distance: '0.5 km' },
				{ name: 'Code & Coffee Co.', rating: 4.9, distance: '0.8 km' },
			],
		},
		{
			type: 'user',
			message: 'Which one has the quietest environment?',
			timestamp: '2:35 PM',
		},
		{
			type: 'ai',
			message: 'Based on user reviews, "Code & Coffee Co." is the quietest with dedicated work zones and noise-canceling design.',
			timestamp: '2:35 PM',
		},
	];

	// Auto-advance conversation steps
	useEffect(() => {
		const interval = setInterval(() => {
			setIsAnimating(true);
			setTimeout(() => {
				setCurrentStep((prev) => (prev + 1) % conversationSteps.length);
				setIsAnimating(false);
			}, 300);
		}, 3000);

		return () => clearInterval(interval);
	}, []);

	const features = [
		{
			icon: FiZap,
			title: 'Instant Understanding',
			description: 'AI comprehends natural language queries instantly',
			color: colors.brand.blue,
		},
		{
			icon: FiMapPin,
			title: 'Contextual Results',
			description: 'Location-aware recommendations based on your needs',
			color: colors.brand.green,
		},
		{
			icon: FiStar,
			title: 'Quality Filtering',
			description: 'Real user reviews and ratings guide suggestions',
			color: colors.brand.navy,
		},
	];

	return (
		<div className='relative py-16 lg:py-24'>
			{/* Background Elements */}
			<div className='absolute inset-0 overflow-hidden'>
				<div
					className='absolute top-10 right-10 w-40 h-40 rounded-full opacity-5 animate-pulse'
					style={{ background: colors.brand.green }}></div>
				<div
					className='absolute bottom-10 left-10 w-32 h-32 rounded-full opacity-5 animate-pulse'
					style={{ background: colors.brand.blue }}></div>
			</div>

			<div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-16'>
					<div className='inline-flex items-center space-x-2 mb-6'>
						<div
							className='w-8 h-8 rounded-full flex items-center justify-center'
							style={{ background: colors.brand.green }}>
							<FiMessageCircle className='w-4 h-4 text-white' />
						</div>
						<span
							className='text-sm font-semibold uppercase tracking-wider'
							style={{ color: colors.brand.green }}>
							See AI In Action
						</span>
					</div>

					<h2
						className='text-3xl sm:text-4xl lg:text-5xl font-bold mb-6'
						style={{ color: colors.brand.navy }}>
						Intelligent Conversations
					</h2>

					<p
						className='text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed'
						style={{ color: colors.neutral.slateGray }}>
						Experience how our AI understands your needs and provides personalized
						recommendations through natural conversation.
					</p>
				</div>

				{/* Main Content Grid */}
				<div className='grid lg:grid-cols-2 gap-12 lg:gap-16 items-center'>
					{/* AI Chat Demo */}
					<div className='order-2 lg:order-1'>
						<div
							className='relative p-6 lg:p-8 rounded-3xl border backdrop-blur-sm'
							style={{
								background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.neutral.cloudWhite} 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 20px 40px rgba(128, 237, 153, 0.1)',
							}}>
							{/* Chat Header */}
							<div className='flex items-center space-x-3 mb-6 pb-4 border-b'>
								<div
									className='w-10 h-10 rounded-full flex items-center justify-center'
									style={{
										background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
									}}>
									<FiMessageCircle className='w-5 h-5 text-white' />
								</div>
								<div>
									<h3
										className='font-semibold text-lg'
										style={{ color: colors.neutral.textBlack }}>
										AI Assistant
									</h3>
									<p
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										Online • Ready to help
									</p>
								</div>
							</div>

							{/* Chat Messages */}
							<div className='space-y-4 h-80 overflow-hidden'>
								{conversationSteps.slice(0, currentStep + 1).map((step, index) => (
									<div
										key={index}
										className={`flex ${step.type === 'user' ? 'justify-end' : 'justify-start'} ${
											isAnimating && index === currentStep ? 'opacity-50' : 'opacity-100'
										} transition-opacity duration-300`}>
										<div
											className={`max-w-[85%] p-4 rounded-2xl ${
												step.type === 'user' ? 'rounded-tr-sm' : 'rounded-tl-sm'
											}`}
											style={{
												background: step.type === 'user' 
													? colors.brand.blue 
													: colors.ui.gray100,
												color: step.type === 'user' 
													? 'white' 
													: colors.neutral.textBlack,
											}}>
											<p className='text-sm lg:text-base mb-2'>{step.message}</p>
											
											{/* AI Results */}
											{step.results && (
												<div className='mt-3 space-y-2'>
													{step.results.map((result, idx) => (
														<div
															key={idx}
															className='flex items-center justify-between p-2 rounded-lg'
															style={{ background: colors.ui.blue50 }}>
															<div className='flex items-center space-x-2'>
																<div
																	className='w-2 h-2 rounded-full'
																	style={{ background: colors.brand.green }}></div>
																<span className='text-xs font-medium text-gray-800'>
																	{result.name}
																</span>
															</div>
															<div className='flex items-center space-x-2 text-xs'>
																<FiStar className='w-3 h-3 text-yellow-500' />
																<span className='text-gray-600'>{result.rating}</span>
																<span className='text-gray-500'>• {result.distance}</span>
															</div>
														</div>
													))}
												</div>
											)}
											
											<div className='flex items-center justify-between mt-2'>
												<span
													className='text-xs'
													style={{ 
														color: step.type === 'user' 
															? 'rgba(255, 255, 255, 0.7)' 
															: colors.neutral.slateGray 
													}}>
													{step.timestamp}
												</span>
												{step.type === 'ai' && index === currentStep && (
													<div className='flex space-x-1'>
														<div
															className='w-1 h-1 rounded-full animate-pulse'
															style={{ background: colors.brand.green }}></div>
														<div
															className='w-1 h-1 rounded-full animate-pulse'
															style={{ 
																background: colors.brand.green,
																animationDelay: '0.2s' 
															}}></div>
														<div
															className='w-1 h-1 rounded-full animate-pulse'
															style={{ 
																background: colors.brand.green,
																animationDelay: '0.4s' 
															}}></div>
													</div>
												)}
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Features List */}
					<div className='order-1 lg:order-2 space-y-8'>
						{features.map((feature, index) => {
							const Icon = feature.icon;
							return (
								<div
									key={index}
									className='flex items-start space-x-4 group hover:scale-105 transition-transform duration-300'>
									<div
										className='w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300'
										style={{
											background: `linear-gradient(135deg, ${feature.color}20 0%, ${feature.color}10 100%)`,
										}}>
										<Icon
											className='w-6 h-6'
											style={{ color: feature.color }}
										/>
									</div>
									<div className='flex-1'>
										<h3
											className='text-xl font-bold mb-2'
											style={{ color: colors.neutral.textBlack }}>
											{feature.title}
										</h3>
										<p
											className='text-base leading-relaxed'
											style={{ color: colors.neutral.slateGray }}>
											{feature.description}
										</p>
									</div>
								</div>
							);
						})}

						{/* CTA Button */}
						<div className='pt-8'>
							<div
								className='inline-flex items-center space-x-3 px-8 py-4 rounded-2xl backdrop-blur-sm border-2 hover:scale-105 transition-all duration-300 cursor-pointer'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.green50}E0 0%, ${colors.ui.blue50}E0 100%)`,
									borderColor: colors.brand.green,
									boxShadow: '0 8px 32px rgba(128, 237, 153, 0.15)',
								}}>
								<FiTrendingUp
									className='w-5 h-5'
									style={{ color: colors.brand.green }}
								/>
								<span
									className='text-lg font-bold'
									style={{ color: colors.brand.navy }}>
									Try AI Search Now
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AIShowcase;
