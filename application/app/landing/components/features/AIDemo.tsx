/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useState } from 'react';
import { FiArrowRight, FiMessageCircle, FiSearch, FiStar, FiZap } from 'react-icons/fi';

interface AIDemoProps {
	onGetStarted: () => void;
}

const AIDemo: React.FC<AIDemoProps> = ({ onGetStarted }) => {
	const [activeDemo, setActiveDemo] = useState(0);

	const demoScenarios = LANDING_PAGE_DATA.aiDemoScenarios;

	return (
		<div className='max-w-6xl mx-auto'>
			{/* Demo Selector */}
			<div className='flex justify-center mb-8'>
				<div className='flex space-x-4'>
					{demoScenarios.map((scenario, index) => (
						<button
							key={scenario.id}
							onClick={() => setActiveDemo(index)}
							className={`px-6 py-3 rounded-xl transition-all duration-300 ${
								activeDemo === index ? 'scale-105' : 'hover:scale-102'
							}`}
							style={{
								background:
									activeDemo === index
										? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
										: colors.ui.gray100,
								color: activeDemo === index ? 'white' : colors.neutral.textBlack,
								boxShadow:
									activeDemo === index
										? '0 10px 25px rgba(51, 194, 255, 0.3)'
										: 'none',
							}}>
							<span className='font-semibold'>Scenario {index + 1}</span>
						</button>
					))}
				</div>
			</div>

			{/* Demo Content */}
			<div className='grid lg:grid-cols-2 gap-8'>
				{/* Left Side - AI Conversation */}
				<div
					className='p-6 rounded-2xl backdrop-blur-sm border'
					style={{
						background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
						borderColor: colors.ui.gray200,
					}}>
					<div className='flex items-center space-x-3 mb-6'>
						<div
							className='w-10 h-10 rounded-xl flex items-center justify-center'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
							}}>
							<FiMessageCircle className='w-5 h-5 text-white' />
						</div>
						<div>
							<h3
								className='font-bold text-lg'
								style={{ color: colors.brand.navy }}>
								AI Conversation
							</h3>
							<p
								className='text-sm'
								style={{ color: colors.neutral.slateGray }}>
								Natural language interaction
							</p>
						</div>
					</div>

					{/* User Message */}
					<div className='mb-4'>
						<div className='flex justify-end mb-2'>
							<div
								className='max-w-[85%] p-4 rounded-2xl rounded-tr-sm'
								style={{ background: colors.brand.blue, color: 'white' }}>
								<p className='text-sm'>
									{demoScenarios[activeDemo].userQuery}
								</p>
							</div>
						</div>
					</div>

					{/* AI Response */}
					<div className='mb-4'>
						<div className='flex justify-start'>
							<div
								className='max-w-[85%] p-4 rounded-2xl rounded-tl-sm'
								style={{
									background: colors.ui.gray100,
									color: colors.neutral.textBlack,
								}}>
								<p className='text-sm'>
									{demoScenarios[activeDemo].aiResponse}
								</p>
								<div className='flex items-center space-x-2 mt-3'>
									<FiZap
										className='w-4 h-4'
										style={{ color: colors.brand.green }}
									/>
									<span
										className='text-xs font-medium'
										style={{ color: colors.brand.green }}>
										AI Processing Complete
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Right Side - Search Results */}
				<div
					className='p-6 rounded-2xl backdrop-blur-sm border'
					style={{
						background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.neutral.cloudWhite} 100%)`,
						borderColor: colors.ui.gray200,
					}}>
					<div className='flex items-center space-x-3 mb-6'>
						<div
							className='w-10 h-10 rounded-xl flex items-center justify-center'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
							}}>
							<FiSearch className='w-5 h-5 text-white' />
						</div>
						<div>
							<h3
								className='font-bold text-lg'
								style={{ color: colors.brand.navy }}>
								Smart Results
							</h3>
							<p
								className='text-sm'
								style={{ color: colors.neutral.slateGray }}>
								Contextual recommendations
							</p>
						</div>
					</div>

					{/* Search Results */}
					<div className='space-y-4'>
						{demoScenarios[activeDemo].searchResults.map((result, index) => (
							<div
								key={index}
								className='p-4 rounded-xl transition-all duration-300 hover:scale-102'
								style={{ background: colors.neutral.cloudWhite }}>
								<div className='flex items-center justify-between mb-2'>
									<h4
										className='font-semibold'
										style={{ color: colors.neutral.textBlack }}>
										{result.name}
									</h4>
									<div className='flex items-center space-x-1'>
										<FiStar
											className='w-4 h-4'
											style={{ color: colors.brand.green }}
										/>
										<span
											className='text-sm font-medium'
											style={{ color: colors.neutral.textBlack }}>
											{result.rating}
										</span>
									</div>
								</div>
								<p
									className='text-sm mb-2'
									style={{ color: colors.neutral.slateGray }}>
									{result.type} • {result.distance}
								</p>
								<div className='flex flex-wrap gap-2'>
									{result.features.map((feature, featureIndex) => (
										<span
											key={featureIndex}
											className='px-2 py-1 rounded-full text-xs font-medium'
											style={{
												background: colors.ui.blue50,
												color: colors.brand.blue,
											}}>
											{feature}
										</span>
									))}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* CTA Button */}
			<div className='text-center mt-8'>
				<button
					onClick={onGetStarted}
					className='group inline-flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105'
					style={{
						background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
						color: 'white',
						boxShadow: '0 10px 30px rgba(51, 194, 255, 0.3)',
					}}>
					<span>Try AI Search Now</span>
					<FiArrowRight className='w-5 h-5 transition-transform duration-300 group-hover:translate-x-1' />
				</button>
			</div>
		</div>
	);
};

export default AIDemo;
