/** @format */

'use client';

import { colors } from '@/app/colors';
import { POI_CATEGORIES_DATA } from '@/app/shared/poi/constants';
import React, { useState } from 'react';
import {
	FiActivity,
	FiBook,
	FiCoffee,
	FiHeart,
	FiMapPin,
	FiMusic,
	FiShoppingBag,
	FiSun,
	FiTrendingUp,
} from 'react-icons/fi';

interface CategoryGridProps {
	onCategorySelect?: (category: any) => void;
}

// Get icon for category
const getCategoryIcon = (category: string) => {
	const categoryIconMap: { [key: string]: JSX.Element } = {
		'Food & Drink': <FiCoffee className='w-8 h-8 text-white' />,
		'Cultural & Creative Experiences': <FiBook className='w-8 h-8 text-white' />,
		'Sports & Fitness': <FiActivity className='w-8 h-8 text-white' />,
		Entertainment: <FiMusic className='w-8 h-8 text-white' />,
		'Shopping & Markets': <FiShoppingBag className='w-8 h-8 text-white' />,
		'Outdoor & Nature': <FiSun className='w-8 h-8 text-white' />,
		'Wellness & Beauty': <FiHeart className='w-8 h-8 text-white' />,
		Transportation: <FiTrendingUp className='w-8 h-8 text-white' />,
	};

	return categoryIconMap[category] || <FiMapPin className='w-8 h-8 text-white' />;
};

// Get gradient for category
const getCategoryGradient = (index: number) => {
	const gradients = [
		`linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
		`linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.navy} 100%)`,
		`linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
	];
	return gradients[index % gradients.length];
};

const CategoryGrid: React.FC<CategoryGridProps> = ({ onCategorySelect }) => {
	const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

	const categories = Object.keys(POI_CATEGORIES_DATA);

	return (
		<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto'>
			{categories.map((category, index) => {
				const subcategories = POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA].subcategories;
				const isHovered = hoveredCategory === category;

				return (
					<div
						key={category}
						className='group relative cursor-pointer transition-all duration-300 hover:scale-105'
						onMouseEnter={() => setHoveredCategory(category)}
						onMouseLeave={() => setHoveredCategory(null)}
						onClick={() => onCategorySelect?.({ category, subcategories })}>
						{/* Card */}
						<div
							className='relative p-6 rounded-2xl backdrop-blur-sm border border-white/20 transition-all duration-300 overflow-hidden'
							style={{
								background: getCategoryGradient(index),
								transform: isHovered ? 'translateY(-5px)' : 'translateY(0)',
								boxShadow: isHovered
									? '0 20px 40px rgba(51, 194, 255, 0.3)'
									: '0 10px 20px rgba(0, 0, 0, 0.1)',
							}}>
							{/* Background pattern */}
							<div
								className='absolute inset-0 opacity-20 transition-all duration-300'
								style={{
									background: `radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%)`,
									transform: isHovered ? 'scale(1.2)' : 'scale(1)',
								}}
							/>

							{/* Content */}
							<div className='relative z-10 text-center space-y-4'>
								{/* Icon */}
								<div className='flex justify-center mb-4'>
									<div
										className='p-3 rounded-xl transition-all duration-300'
										style={{
											background: 'rgba(255, 255, 255, 0.2)',
											transform: isHovered ? 'scale(1.1) rotate(5deg)' : 'scale(1)',
										}}>
										{getCategoryIcon(category)}
									</div>
								</div>

								{/* Category name */}
								<h3 className='text-xl font-bold text-white leading-tight'>
									{category}
								</h3>

								{/* Subcategory count */}
								<p className='text-white/80 text-sm'>
									{subcategories.length} subcategories
								</p>

								{/* Hover indicator */}
								<div
									className='absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 rounded-full transition-all duration-300'
									style={{
										background: 'rgba(255, 255, 255, 0.5)',
										width: isHovered ? '60%' : '30%',
									}}
								/>
							</div>

							{/* Floating particles */}
							<div className='absolute top-2 right-2 w-2 h-2 rounded-full bg-white/30 animate-pulse' />
							<div className='absolute bottom-4 left-3 w-1 h-1 rounded-full bg-white/40 animate-pulse delay-500' />
						</div>
					</div>
				);
			})}
		</div>
	);
};

export default CategoryGrid;
