/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useState } from 'react';
import {
	FiGrid,
	FiMapPin,
	FiSearch,
	FiFilter,
	FiTrendingUp,
	FiStar,
} from 'react-icons/fi';

const CategoryExplorer: React.FC = () => {
	const [hoveredCategory, setHoveredCategory] = useState<number | null>(null);

	// Sample categories for demonstration
	const categories = [
		{
			name: 'Restaurants',
			icon: FiGrid,
			count: 24,
			color: colors.brand.blue,
			description: 'From fine dining to street food',
		},
		{
			name: 'Entertainment',
			icon: FiStar,
			count: 18,
			color: colors.brand.green,
			description: 'Clubs, bars, and nightlife',
		},
		{
			name: 'Shopping',
			icon: FiTrendingUp,
			count: 16,
			color: colors.brand.navy,
			description: 'Malls, markets, and boutiques',
		},
		{
			name: 'Outdoor',
			icon: FiMapPin,
			count: 12,
			color: colors.supporting.lightBlue,
			description: 'Parks, beaches, and nature',
		},
		{
			name: 'Culture',
			icon: FiFilter,
			count: 14,
			color: colors.supporting.mintGreen,
			description: 'Museums, galleries, and history',
		},
		{
			name: 'Sports',
			icon: FiSearch,
			count: 10,
			color: colors.brand.blue,
			description: 'Gyms, courts, and activities',
		},
	];

	return (
		<div className='relative py-16 lg:py-24'>
			{/* Background Elements */}
			<div className='absolute inset-0 overflow-hidden'>
				<div
					className='absolute top-20 left-10 w-32 h-32 rounded-full opacity-10 animate-pulse'
					style={{ background: colors.brand.blue }}></div>
				<div
					className='absolute bottom-20 right-10 w-24 h-24 rounded-full opacity-10 animate-pulse'
					style={{ background: colors.brand.green }}></div>
			</div>

			<div className='relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
				{/* Section Header */}
				<div className='text-center mb-16'>
					<div className='inline-flex items-center space-x-2 mb-6'>
						<div
							className='w-8 h-8 rounded-full flex items-center justify-center'
							style={{ background: colors.brand.blue }}>
							<FiGrid className='w-4 h-4 text-white' />
						</div>
						<span
							className='text-sm font-semibold uppercase tracking-wider'
							style={{ color: colors.brand.blue }}>
							Explore Every Category
						</span>
					</div>

					<h2
						className='text-3xl sm:text-4xl lg:text-5xl font-bold mb-6'
						style={{ color: colors.brand.navy }}>
						Discover 200+ Subcategories
					</h2>

					<p
						className='text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed'
						style={{ color: colors.neutral.slateGray }}>
						From hidden gems to popular destinations, explore every type of location
						with our comprehensive category system powered by real user data.
					</p>
				</div>

				{/* Category Grid */}
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8'>
					{categories.map((category, index) => {
						const Icon = category.icon;
						const isHovered = hoveredCategory === index;

						return (
							<div
								key={index}
								className='group cursor-pointer'
								onMouseEnter={() => setHoveredCategory(index)}
								onMouseLeave={() => setHoveredCategory(null)}>
								<div
									className='relative p-6 lg:p-8 rounded-2xl border backdrop-blur-sm transition-all duration-500 hover:shadow-2xl'
									style={{
										background: isHovered
											? `linear-gradient(135deg, ${category.color}15 0%, ${colors.neutral.cloudWhite} 100%)`
											: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
										borderColor: isHovered ? category.color : colors.ui.gray200,
										transform: isHovered ? 'translateY(-8px) scale(1.02)' : 'translateY(0) scale(1)',
										boxShadow: isHovered
											? `0 20px 40px ${category.color}20`
											: '0 4px 12px rgba(0, 0, 0, 0.05)',
									}}>
									{/* Icon */}
									<div
										className='w-12 h-12 lg:w-16 lg:h-16 rounded-xl flex items-center justify-center mb-4 transition-all duration-500'
										style={{
											background: isHovered
												? `linear-gradient(135deg, ${category.color} 0%, ${colors.brand.blue} 100%)`
												: `linear-gradient(135deg, ${category.color}20 0%, ${category.color}10 100%)`,
											transform: isHovered ? 'scale(1.1) rotate(5deg)' : 'scale(1) rotate(0deg)',
										}}>
										<Icon
											className='w-6 h-6 lg:w-8 lg:h-8 transition-all duration-500'
											style={{
												color: isHovered ? 'white' : category.color,
											}}
										/>
									</div>

									{/* Content */}
									<div className='space-y-3'>
										<div className='flex items-center justify-between'>
											<h3
												className='text-lg lg:text-xl font-bold transition-colors duration-300'
												style={{
													color: isHovered ? category.color : colors.neutral.textBlack,
												}}>
												{category.name}
											</h3>
											<span
												className='text-sm font-semibold px-2 py-1 rounded-full transition-all duration-300'
												style={{
													background: isHovered ? category.color : `${category.color}20`,
													color: isHovered ? 'white' : category.color,
												}}>
												{category.count}
											</span>
										</div>

										<p
											className='text-sm lg:text-base leading-relaxed transition-colors duration-300'
											style={{
												color: isHovered ? colors.neutral.textBlack : colors.neutral.slateGray,
											}}>
											{category.description}
										</p>
									</div>

									{/* Hover Effect Indicator */}
									<div
										className='absolute bottom-0 left-0 h-1 rounded-b-2xl transition-all duration-500'
										style={{
											width: isHovered ? '100%' : '0%',
											background: `linear-gradient(90deg, ${category.color} 0%, ${colors.brand.blue} 100%)`,
										}}></div>
								</div>
							</div>
						);
					})}
				</div>

				{/* Bottom CTA */}
				<div className='text-center mt-16'>
					<div
						className='inline-flex items-center space-x-3 px-8 py-4 rounded-2xl backdrop-blur-sm border-2 hover:scale-105 transition-all duration-300 cursor-pointer'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.blue50}E0 0%, ${colors.ui.green50}E0 100%)`,
							borderColor: colors.brand.blue,
							boxShadow: '0 8px 32px rgba(51, 194, 255, 0.15)',
						}}>
						<FiSearch
							className='w-5 h-5'
							style={{ color: colors.brand.blue }}
						/>
						<span
							className='text-lg font-bold'
							style={{ color: colors.brand.navy }}>
							Explore All Categories
						</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default CategoryExplorer;
