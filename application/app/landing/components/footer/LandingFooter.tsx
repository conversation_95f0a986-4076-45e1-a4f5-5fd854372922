/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React from 'react';
import {
	FiMapPin,
	FiMail,
	FiGithub,
	FiTwitter,
	FiLinkedin,
	FiGlobe,
	FiHeart,
	FiArrowUp,
} from 'react-icons/fi';

const LandingFooter: React.FC = () => {
	const scrollToTop = () => {
		window.scrollTo({ top: 0, behavior: 'smooth' });
	};

	const footerLinks = {
		product: [
			{ name: 'Features', href: '#features' },
			{ name: 'Categories', href: '#categories' },
			{ name: 'AI Search', href: '#ai-search' },
			{ name: 'Pricing', href: '#pricing' },
		],
		company: [
			{ name: 'About Us', href: '#about' },
			{ name: 'Blog', href: '#blog' },
			{ name: 'Careers', href: '#careers' },
			{ name: 'Contact', href: '#contact' },
		],
		support: [
			{ name: 'Help Center', href: '#help' },
			{ name: 'Documentation', href: '#docs' },
			{ name: 'API Reference', href: '#api' },
			{ name: 'Status', href: '#status' },
		],
		legal: [
			{ name: 'Privacy Policy', href: '#privacy' },
			{ name: 'Terms of Service', href: '#terms' },
			{ name: 'Cookie Policy', href: '#cookies' },
			{ name: 'GDPR', href: '#gdpr' },
		],
	};

	const socialLinks = [
		{ icon: FiTwitter, href: '#twitter', label: 'Twitter' },
		{ icon: FiLinkedin, href: '#linkedin', label: 'LinkedIn' },
		{ icon: FiGithub, href: '#github', label: 'GitHub' },
		{ icon: FiMail, href: '#email', label: 'Email' },
	];

	return (
		<footer className='relative'>
			{/* Background */}
			<div
				className='absolute inset-0'
				style={{
					background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
				}}></div>

			{/* Content */}
			<div className='relative'>
				{/* Main Footer Content */}
				<div className='px-4 sm:px-6 lg:px-8 py-16 lg:py-20'>
					<div className='max-w-7xl mx-auto'>
						{/* Top Section */}
						<div className='grid grid-cols-1 lg:grid-cols-4 gap-12 lg:gap-8 mb-16'>
							{/* Brand Section */}
							<div className='lg:col-span-1 space-y-6'>
								{/* Logo and Brand */}
								<div className='flex items-center space-x-3'>
									<img
										src='/logo/512x512.png'
										alt='Wizlop Logo'
										className='w-10 h-10 object-contain'
										style={{
											filter: 'brightness(0) invert(1)',
										}}
									/>
									<h3 className='text-2xl font-black text-white'>
										{LANDING_PAGE_DATA.branding.companyName}
									</h3>
								</div>

								{/* Description */}
								<p className='text-blue-100 leading-relaxed'>
									Discover amazing places with AI-powered search. From hidden gems to
									popular destinations, find exactly what you're looking for.
								</p>

								{/* Social Links */}
								<div className='flex space-x-4'>
									{socialLinks.map(({ icon: Icon, href, label }) => (
										<a
											key={label}
											href={href}
											className='w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all duration-300 hover:scale-110'
											aria-label={label}>
											<Icon className='w-5 h-5 text-white' />
										</a>
									))}
								</div>
							</div>

							{/* Links Sections */}
							<div className='lg:col-span-3 grid grid-cols-2 md:grid-cols-4 gap-8'>
								{/* Product */}
								<div>
									<h4 className='text-white font-semibold mb-4'>Product</h4>
									<ul className='space-y-3'>
										{footerLinks.product.map((link) => (
											<li key={link.name}>
												<a
													href={link.href}
													className='text-blue-100 hover:text-white transition-colors duration-300'>
													{link.name}
												</a>
											</li>
										))}
									</ul>
								</div>

								{/* Company */}
								<div>
									<h4 className='text-white font-semibold mb-4'>Company</h4>
									<ul className='space-y-3'>
										{footerLinks.company.map((link) => (
											<li key={link.name}>
												<a
													href={link.href}
													className='text-blue-100 hover:text-white transition-colors duration-300'>
													{link.name}
												</a>
											</li>
										))}
									</ul>
								</div>

								{/* Support */}
								<div>
									<h4 className='text-white font-semibold mb-4'>Support</h4>
									<ul className='space-y-3'>
										{footerLinks.support.map((link) => (
											<li key={link.name}>
												<a
													href={link.href}
													className='text-blue-100 hover:text-white transition-colors duration-300'>
													{link.name}
												</a>
											</li>
										))}
									</ul>
								</div>

								{/* Legal */}
								<div>
									<h4 className='text-white font-semibold mb-4'>Legal</h4>
									<ul className='space-y-3'>
										{footerLinks.legal.map((link) => (
											<li key={link.name}>
												<a
													href={link.href}
													className='text-blue-100 hover:text-white transition-colors duration-300'>
													{link.name}
												</a>
											</li>
										))}
									</ul>
								</div>
							</div>
						</div>

						{/* Newsletter Section */}
						<div
							className='p-8 rounded-2xl mb-12'
							style={{
								background: 'rgba(255, 255, 255, 0.1)',
								backdropFilter: 'blur(10px)',
							}}>
							<div className='text-center max-w-2xl mx-auto'>
								<h4 className='text-2xl font-bold text-white mb-4'>
									Stay Updated
								</h4>
								<p className='text-blue-100 mb-6'>
									Get the latest updates on new features, categories, and AI improvements.
								</p>
								<div className='flex flex-col sm:flex-row gap-4 max-w-md mx-auto'>
									<input
										type='email'
										placeholder='Enter your email'
										className='flex-1 px-4 py-3 rounded-xl bg-white/20 border border-white/30 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/50'
									/>
									<button
										className='px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105'
										style={{
											background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
											color: colors.brand.navy,
										}}>
										Subscribe
									</button>
								</div>
							</div>
						</div>

						{/* Bottom Section */}
						<div className='border-t border-white/20 pt-8'>
							<div className='flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0'>
								{/* Copyright */}
								<div className='flex items-center space-x-2 text-blue-100'>
									<span>© 2024 {LANDING_PAGE_DATA.branding.companyName}.</span>
									<span>Made with</span>
									<FiHeart className='w-4 h-4 text-red-400' />
									<span>in Istanbul</span>
								</div>

								{/* Stats */}
								<div className='flex items-center space-x-6 text-blue-100'>
									<div className='flex items-center space-x-2'>
										<FiMapPin className='w-4 h-4' />
										<span className='text-sm'>8 Categories</span>
									</div>
									<div className='flex items-center space-x-2'>
										<FiGlobe className='w-4 h-4' />
										<span className='text-sm'>96 Subcategories</span>
									</div>
								</div>

								{/* Scroll to Top */}
								<button
									onClick={scrollToTop}
									className='w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all duration-300 hover:scale-110'
									aria-label='Scroll to top'>
									<FiArrowUp className='w-5 h-5 text-white' />
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</footer>
	);
};

export default LandingFooter;
